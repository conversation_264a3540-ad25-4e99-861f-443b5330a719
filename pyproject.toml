# Copyright 2025 Google LLC.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

[build-system]
requires = ["setuptools>=67.0.0", "wheel"]
build-backend = "setuptools.build_meta"


[project]
name = "langextract"
version = "1.0.9"
description = "LangExtract: A library for extracting structured data from language models"
readme = "README.md"
requires-python = ">=3.10"
license = "Apache-2.0"
authors = [
    {name = "Akshay <PERSON>", email = "<EMAIL>"}
]
dependencies = [
    "absl-py>=1.0.0",
    "aiohttp>=3.8.0",
    "async_timeout>=4.0.0",
    "exceptiongroup>=1.1.0",
    "google-genai>=0.1.0",
    "ml-collections>=0.1.0",
    "more-itertools>=8.0.0",
    "numpy>=1.20.0",
    "pandas>=1.3.0",
    "pydantic>=1.8.0",
    "python-dotenv>=0.19.0",
    "PyYAML>=6.0",
    "requests>=2.25.0",
    "tqdm>=4.64.0",
    "typing-extensions>=4.0.0"
]

[project.urls]
"Homepage" = "https://github.com/google/langextract"
"Repository" = "https://github.com/google/langextract"
"Documentation" = "https://github.com/google/langextract/blob/main/README.md"
"Bug Tracker" = "https://github.com/google/langextract/issues"
"Changelog" = "https://github.com/google/langextract/releases"
"DOI" = "https://doi.org/10.5281/zenodo.17015089"

[project.optional-dependencies]
openai = ["openai>=1.50.0"]
all = ["openai>=1.50.0"]
dev = [
    "pyink~=24.3.0",
    "isort>=5.13.0",
    "pylint>=3.0.0",
    "pytype>=2024.10.11",
    "tox>=4.0.0",
    "import-linter>=2.0"
]
test = [
    "pytest>=7.4.0",
    "tomli>=2.0.0"
]
notebook = [
    "ipython>=7.0.0",
    "notebook>=6.0.0"
]

[tool.setuptools]
packages = [
    "langextract",
    "langextract._compat",
    "langextract.core",
    "langextract.providers",
    "langextract.providers.schemas"
]
include-package-data = true

[tool.setuptools.package-data]
langextract = ["py.typed"]

# Provider discovery mechanism for built-in and third-party providers
[project.entry-points."langextract.providers"]
gemini = "langextract.providers.gemini:GeminiLanguageModel"
ollama = "langextract.providers.ollama:OllamaLanguageModel"
openai = "langextract.providers.openai:OpenAILanguageModel"

[tool.setuptools.exclude-package-data]
"*" = [
    "docs*",
    "tests*",
    "kokoro*",
    "*.gif",
    "*.svg",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "*_test.py"
python_classes = "Test*"
python_functions = "test_*"
# Show extra test summary info
addopts = "-ra"
markers = [
    "live_api: marks tests as requiring live API access",
    "requires_pip: marks tests that perform pip install/uninstall operations",
    "integration: marks integration tests that test multiple components together",
]

[tool.pyink]
# Configuration for Google's style guide
line-length = 80
unstable = true
pyink-indentation = 2
pyink-use-majority-quotes = true

[tool.isort]
# Configuration for Google's style guide
profile = "google"
line_length = 80
force_sort_within_sections = true
# Allow multiple imports on one line for these modules
single_line_exclusions = ["typing", "typing_extensions", "collections.abc"]

[tool.importlinter]
root_package = "langextract"


[[tool.importlinter.contracts]]
name = "Providers must not import inference"
type = "forbidden"
source_modules = ["langextract.providers"]
forbidden_modules = ["langextract.inference"]

[[tool.importlinter.contracts]]
name = "Core must not import providers"
type = "forbidden"
source_modules = ["langextract.core"]
forbidden_modules = ["langextract.providers"]

[[tool.importlinter.contracts]]
name = "Core must not import high-level modules"
type = "forbidden"
source_modules = ["langextract.core"]
forbidden_modules = [
  "langextract.annotation",
  "langextract.chunking",
  "langextract.prompting",
  "langextract.resolver",
]
