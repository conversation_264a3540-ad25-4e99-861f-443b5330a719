# SPDX-FileCopyrightText: 2025 Google LLC
# SPDX-License-Identifier: Apache-2.0
#
# This file contains citation metadata for LangExtract.
# For more information visit: https://citation-file-format.github.io/

cff-version: 1.2.0
title: "LangExtract"
message: "If you use this software, please cite it as below."
type: software
authors:
  - given-names: Akshay
    family-names: Goel
    email: <EMAIL>
    affiliation: Google LLC
repository-code: "https://github.com/google/langextract"
url: "https://github.com/google/langextract"
repository: "https://github.com/google/langextract"
abstract: "LangExtract: LLM-powered structured information extraction from text with source grounding"
keywords:
  - language-models
  - structured-data-extraction
  - nlp
  - machine-learning
  - python
license: Apache-2.0
version: 1.0.3
date-released: 2025-07-30

doi: "10.5281/zenodo.17015089"
identifiers:
  - type: doi
    value: "10.5281/zenodo.17015089"
    description: "Concept DOI for LangExtract"
