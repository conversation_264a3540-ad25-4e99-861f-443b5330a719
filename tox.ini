# Copyright 2025 Google LLC.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

[tox]
envlist = py310, py311, py312, format, lint-src, lint-tests
skip_missing_interpreters = True

[testenv]
setenv =
    PYTHONWARNINGS = ignore
deps =
    .[openai,dev,test]
commands =
    pytest -ra -m "not live_api and not requires_pip"

[testenv:format]
skip_install = true
deps =
    isort>=5.13.2
    pyink~=24.3.0
commands =
    isort langextract tests --check-only --diff
    pyink langextract tests --check --diff --config pyproject.toml

[testenv:lint-src]
deps =
    pylint>=3.0.0
commands =
    pylint --rcfile=.pylintrc langextract

[testenv:lint-tests]
deps =
    pylint>=3.0.0
commands =
    pylint --rcfile=tests/.pylintrc tests

[testenv:live-api]
basepython = python3.11
passenv =
    GEMINI_API_KEY
    LANGEXTRACT_API_KEY
    OPENAI_API_KEY
deps = .[all,dev,test]
commands =
    pytest tests/test_live_api.py -v -m live_api --maxfail=1

[testenv:ollama-integration]
basepython = python3.11
deps =
    .[openai,dev,test]
    requests>=2.25.0
commands =
    pytest tests/test_ollama_integration.py -v --tb=short

[testenv:plugin-integration]
basepython = python3.11
setenv =
    PIP_NO_INPUT = 1
    PIP_DISABLE_PIP_VERSION_CHECK = 1
deps =
    .[dev,test]
commands =
    pytest tests/provider_plugin_test.py::PluginE2ETest -v -m "requires_pip"

[testenv:plugin-smoke]
basepython = python3.11
deps =
    .[dev,test]
commands =
    pytest tests/provider_plugin_test.py::PluginSmokeTest -v
