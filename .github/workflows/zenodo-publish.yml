# Copyright 2025 Google LLC.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: Publish to Zenodo
on:
  release:
    types: [published]

concurrency:
  group: zenodo-${{ github.ref }}
  cancel-in-progress: false

jobs:
  zenodo:
    # Only run on releases from the main repository, not forks
    # Skip pre-releases to avoid creating DOIs for test releases
    if: ${{ !github.event.release.prerelease && github.repository == 'google/langextract' }}
    runs-on: ubuntu-latest
    timeout-minutes: 15
    permissions:
      contents: read
    env:
      ZENODO_TOKEN: ${{ secrets.ZENODO_TOKEN }}
      ZENODO_RECORD_ID: ${{ secrets.ZENODO_RECORD_ID }}
      RELEASE_TAG: ${{ github.ref_name }}
      GITHUB_REPOSITORY: ${{ github.repository }}
    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Build distributions
        run: |
          python -m pip install --upgrade pip build
          python -m build

      - name: Install dependencies
        run: python -m pip install requests

      - name: Publish new Zenodo version
        run: python .github/scripts/zenodo_publish.py
